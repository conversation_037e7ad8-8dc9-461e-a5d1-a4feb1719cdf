#############################################################################
# Makefile for building: serial_port
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\..\serial_port.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_QML_DEBUG -DQT_CHARTS_LIB -DQT_OPENGLWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_OPENGL_LIB -DQT_GUI_LIB -DQT_SERIALPORT_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../../serial_port -I. -ID:/Qt/6.9.1/mingw_64/include -ID:/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Qt/6.9.1/mingw_64/include/QtGui -ID:/Qt/6.9.1/mingw_64/include/QtSerialPort -ID:/Qt/6.9.1/mingw_64/include/QtCore -Idebug -I. -I/include -ID:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Qt\6.9.1\mingw_64\lib\libQt6Charts.a D:\Qt\6.9.1\mingw_64\lib\libQt6OpenGLWidgets.a D:\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a D:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a D:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a D:\Qt\6.9.1\mingw_64\lib\libQt6SerialPort.a D:\Qt\6.9.1\mingw_64\lib\libQt6Core.a -lmingw32 D:\Qt\6.9.1\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\main.cpp \
		..\..\mainwindow.cpp debug\moc_mainwindow.cpp
OBJECTS       = debug/main.o \
		debug/mainwindow.o \
		debug/moc_mainwindow.o

DIST          =  ..\..\mainwindow.h ..\..\main.cpp \
		..\..\mainwindow.cpp
QMAKE_TARGET  = serial_port
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = serial_port.exe
DESTDIR_TARGET = debug\serial_port.exe

####### Build rules

first: all
all: Makefile.Debug  debug/serial_port.exe

debug/serial_port.exe: D:/Qt/6.9.1/mingw_64/lib/libQt6Charts.a D:/Qt/6.9.1/mingw_64/lib/libQt6OpenGLWidgets.a D:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a D:/Qt/6.9.1/mingw_64/lib/libQt6OpenGL.a D:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a D:/Qt/6.9.1/mingw_64/lib/libQt6SerialPort.a D:/Qt/6.9.1/mingw_64/lib/libQt6Core.a D:/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a ui_mainwindow.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\serial_port.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) serial_port.zip $(SOURCES) $(DIST) ..\..\..\..\serial_port.pro D:\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf D:\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf D:\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf D:\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf D:\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf D:\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf D:\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf D:\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_serialport.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf D:\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf D:\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf .qmake.stash D:\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\qml_debug.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf D:\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf ..\..\serial_port.pro D:\Qt\6.9.1\mingw_64\lib\Qt6Charts.prl D:\Qt\6.9.1\mingw_64\lib\Qt6OpenGLWidgets.prl D:\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl D:\Qt\6.9.1\mingw_64\lib\Qt6OpenGL.prl D:\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl D:\Qt\6.9.1\mingw_64\lib\Qt6SerialPort.prl D:\Qt\6.9.1\mingw_64\lib\Qt6Core.prl D:\Qt\6.9.1\mingw_64\lib\Qt6EntryPoint.prl    D:\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp ..\..\mainwindow.h  ..\..\main.cpp ..\..\mainwindow.cpp ..\..\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\mainwindow.o debug\moc_mainwindow.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: D:/Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h D:\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_mainwindow.cpp
debug/moc_mainwindow.cpp: ../../mainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPort \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qtserialportexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPortInfo \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtCharts \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtChartsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGL \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGLDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengldebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglframebufferobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpixeltransferoptions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglshaderprogram.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltexture.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltextureblitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix3x3 \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltimerquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctionsfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionprofile.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglvertexarrayobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QOpenGLContext \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QImage \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qopenglwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPen \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qarealegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegendMarker \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAreaSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qareaseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlesticklegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcoloraxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qdatetimeaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxyseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlogvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpielegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSlice \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieslice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpolarchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qscatterseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qsplineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxylegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVector \
		debug/moc_predefs.h \
		D:/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/debug/moc_predefs.h -ID:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -ID:/HuaweiMoveData/Users/<USER>/Documents/serial_port -ID:/Qt/6.9.1/mingw_64/include -ID:/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Qt/6.9.1/mingw_64/include/QtGui -ID:/Qt/6.9.1/mingw_64/include/QtSerialPort -ID:/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -ID:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -ID:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -ID:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -ID:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -ID:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\mainwindow.h -o debug\moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ../../mainwindow.ui \
		D:/Qt/6.9.1/mingw_64/bin/uic.exe
	D:\Qt\6.9.1\mingw_64\bin\uic.exe ..\..\mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: ../../main.cpp ../../mainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPort \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qtserialportexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPortInfo \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtCharts \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtChartsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGL \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGLDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengldebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglframebufferobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpixeltransferoptions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglshaderprogram.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltexture.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltextureblitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix3x3 \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltimerquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctionsfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionprofile.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglvertexarrayobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QOpenGLContext \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QImage \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qopenglwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPen \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qarealegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegendMarker \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAreaSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qareaseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlesticklegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcoloraxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qdatetimeaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxyseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlogvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpielegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSlice \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieslice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpolarchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qscatterseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qsplineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxylegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVector \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\main.cpp

debug/mainwindow.o: ../../mainwindow.cpp ../../mainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPort \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialport.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qtserialportexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPortInfo \
		D:/Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtCharts \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QtChartsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGL \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/QtOpenGLDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglbuffer.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengldebug.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglframebufferobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpaintdevice.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglpixeltransferoptions.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglshaderprogram.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltexture.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltextureblitter.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix3x3 \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopengltimerquery.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctions.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionfunctionsfactory.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglversionprofile.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglvertexarrayobject.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qopenglwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QOpenGLContext \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QImage \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGL/qtopenglversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgets \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/QtOpenGLWidgetsDepends \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qopenglwidget.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets/qtopenglwidgetsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QPen \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qarealegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegendMarker \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		D:/Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAreaSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qareaseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotlegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxSet \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlesticklegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcandlestickset.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcategoryaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qcoloraxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qdatetimeaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBarModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QBoxPlotModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QCandlestickModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhorizontalstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qhxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYModelMapper \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QXYSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxyseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qlogvalueaxis.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpercentbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpielegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSeries \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/QPieSlice \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpieslice.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qpolarchart.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qscatterseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qsplineseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qstackedbarseries.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsversion.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvbarmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvboxplotmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvcandlestickmodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvpiemodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qvxymodelmapper.h \
		D:/Qt/6.9.1/mingw_64/include/QtCharts/qxylegendmarker.h \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QVector \
		ui_mainwindow.h \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QMenuBar \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		D:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Qt/6.9.1/mingw_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\mainwindow.o ..\..\mainwindow.cpp

debug/moc_mainwindow.o: debug/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_mainwindow.o debug\moc_mainwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

