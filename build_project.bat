@echo off
echo 正在编译串口可视化软件...
echo.

REM 设置Qt环境变量
set QTDIR=D:\Qt\6.9.1\mingw_64
set PATH=%QTDIR%\bin;%PATH%

REM 检查Qt是否存在
if not exist "%QTDIR%\bin\qmake.exe" (
    echo 错误: 找不到Qt安装目录 %QTDIR%
    echo 请检查Qt安装路径是否正确
    pause
    exit /b 1
)

REM 清理旧的构建文件
if exist Makefile del Makefile
if exist *.o del *.o
if exist moc_*.cpp del moc_*.cpp
if exist ui_*.h del ui_*.h

echo 步骤1: 运行qmake生成Makefile...
"%QTDIR%\bin\qmake.exe" serial_port.pro
if errorlevel 1 (
    echo 错误: qmake执行失败
    pause
    exit /b 1
)

echo 步骤2: 查找make工具...
REM 尝试不同的make工具路径
set MAKE_TOOL=
if exist "%QTDIR%\..\Tools\mingw1310_64\bin\mingw32-make.exe" (
    set MAKE_TOOL=%QTDIR%\..\Tools\mingw1310_64\bin\mingw32-make.exe
) else if exist "%QTDIR%\..\Tools\mingw_64\bin\mingw32-make.exe" (
    set MAKE_TOOL=%QTDIR%\..\Tools\mingw_64\bin\mingw32-make.exe
) else if exist "C:\Qt\Tools\mingw1310_64\bin\mingw32-make.exe" (
    set MAKE_TOOL=C:\Qt\Tools\mingw1310_64\bin\mingw32-make.exe
) else (
    echo 警告: 找不到mingw32-make.exe，尝试使用系统make...
    where make >nul 2>&1
    if errorlevel 1 (
        echo 错误: 找不到make工具
        echo 请确保MinGW已正确安装
        pause
        exit /b 1
    )
    set MAKE_TOOL=make
)

echo 找到make工具: %MAKE_TOOL%
echo 步骤3: 编译项目...
"%MAKE_TOOL%"
if errorlevel 1 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件位置: debug\serial_port.exe 或 release\serial_port.exe
echo.
pause
