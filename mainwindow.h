#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QThread>
#include <QVector>
#include <QChartView>
#include <QBarSeries>
#include <QBarSet>
#include <QBarCategoryAxis>
#include <QValueAxis>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QGroupBox>
#include <QStatusBar>
#include <QTextEdit>
#include <QDateTime>
#include <QDebug>
#include <QMutex>
#include <QAtomicInt>
#include <QScopedPointer>

QT_BEGIN_NAMESPACE
class QSerialPort;
class QSerialPortInfo;
QT_END_NAMESPACE

// 常量定义
namespace SerialPortConstants {
    const int DEFAULT_BAUD_RATE = 250000;
    const int UPDATE_INTERVAL_MS = 100;
    const int SIMULATION_INTERVAL_MS = 200;
    const int SENSOR_COUNT = 10;
    const int DISPLAY_SENSOR_COUNT = 5;
    const int MAX_CHART_VALUE = 4095;
    const int THREAD_WAIT_TIMEOUT_MS = 3000;
    const double RATE_UPDATE_INTERVAL_SEC = 0.5;
}

// 使用Qt Charts命名空间
QT_CHARTS_USE_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void refreshPorts();
    void toggleConnection();
    void processData(const QString &data);
    void updateDisplay();
    void updateScaleFactor(int index, double value);
    void clearLog();

private:
    void initializeChart();
    QWidget *createControlPanel();
    void updateChart();
    void generateSimulatedData();

    // UI控件
    QComboBox *portCombo;
    QComboBox *baudCombo;
    QPushButton *connectBtn;
    QPushButton *pauseBtn;
    QLabel *dataCountLabel;
    QLabel *dataRateLabel;
    QTextEdit *logText;
    QStatusBar *statusBar;
    QChartView *chartView;

    // 图表组件
    QChart *chart;
    QBarSeries *barSeries;
    QBarSet *barSet;
    QBarCategoryAxis *axisX;
    QValueAxis *axisY;

    // 数据相关
    QScopedPointer<class SerialReader> serialReader;
    QTimer *updateTimer;
    QTimer *simulationTimer;
    QVector<QString> sensorNames;
    QVector<double> scaleFactors;
    QVector<double> currentValues;
    QMutex dataMutex; // 保护共享数据
    int dataPointsCounter;
    double dataRate;
    qint64 lastTime;
};

// 将SerialReader类定义移到MainWindow类定义之后
class SerialReader : public QThread
{
    Q_OBJECT
public:
    SerialReader(const QString &portName, int baudRate, QObject *parent = nullptr);
    void stop();
    bool isConnected() const;

signals:
    void dataReady(const QString &data);
    void connectionError(const QString &error);

protected:
    void run() override;

private:
    QString portName;
    int baudRate;
    QAtomicInt running; // 使用原子操作保证线程安全
    mutable QMutex connectionMutex; // 保护连接状态
    bool connected;
};

#endif // MAINWINDOW_H
