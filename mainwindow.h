#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QThread>
#include <QVector>
#include <QChartView>
#include <QBarSeries>
#include <QBarSet>
#include <QBarCategoryAxis>
#include <QValueAxis>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QGroupBox>
#include <QStatusBar>
#include <QTextEdit>
#include <QDateTime>
#include <QDebug>

QT_BEGIN_NAMESPACE
class QSerialPort;
class QSerialPortInfo;
QT_END_NAMESPACE

QT_CHARTS_USE_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void refreshPorts();
    void toggleConnection();
    void processData(const QString &data);
    void updateDisplay();
    void updateScaleFactor(int index, double value);
    void clearLog();

private:
    void initializeChart();
    QWidget *createControlPanel();
    void updateChart();
    void generateSimulatedData();

    // UI控件
    QComboBox *portCombo;
    QComboBox *baudCombo;
    QPushButton *connectBtn;
    QPushButton *pauseBtn;
    QLabel *dataCountLabel;
    QLabel *dataRateLabel;
    QTextEdit *logText;
    QStatusBar *statusBar;
    QChartView *chartView;

    // 图表组件
    QChart *chart;
    QBarSeries *barSeries;
    QBarSet *barSet;
    QBarCategoryAxis *axisX;
    QValueAxis *axisY;

    // 数据相关
    class SerialReader *serialReader;
    QTimer *updateTimer;
    QTimer *simulationTimer;
    QVector<QString> sensorNames;
    QVector<double> scaleFactors;
    QVector<double> currentValues;
    int dataPointsCounter;
    double dataRate;
    qint64 lastTime;
};

// 将SerialReader类定义移到MainWindow类定义之后
class SerialReader : public QThread
{
    Q_OBJECT
public:
    SerialReader(const QString &portName, int baudRate, QObject *parent = nullptr);
    void stop();

signals:
    void dataReady(const QString &data);

protected:
    void run() override;

private:
    QString portName;
    int baudRate;
    bool running;
};

QT_CHARTS_USE_NAMESPACE

#endif // MAINWINDOW_H
