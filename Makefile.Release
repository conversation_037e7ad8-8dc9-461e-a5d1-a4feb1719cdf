#############################################################################
# Makefile for building: serial_port
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  serial_port.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_OPENGLWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_OPENGL_LIB -DQT_GUI_LIB -DQT_SERIALPORT_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -I../Qt/6.9.1/mingw_64/include -I../Qt/6.9.1/mingw_64/include/QtCharts -I../Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -I../Qt/6.9.1/mingw_64/include/QtWidgets -I../Qt/6.9.1/mingw_64/include/QtOpenGL -I../Qt/6.9.1/mingw_64/include/QtGui -I../Qt/6.9.1/mingw_64/include/QtSerialPort -I../Qt/6.9.1/mingw_64/include/QtCore -Irelease -I. -I/include -I../Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Qt\6.9.1\mingw_64\lib\libQt6Charts.a D:\Qt\6.9.1\mingw_64\lib\libQt6OpenGLWidgets.a D:\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a D:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a D:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a D:\Qt\6.9.1\mingw_64\lib\libQt6SerialPort.a D:\Qt\6.9.1\mingw_64\lib\libQt6Core.a -lmingw32 D:\Qt\6.9.1\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = main.cpp \
		mainwindow.cpp release\moc_mainwindow.cpp
OBJECTS       = release/main.o \
		release/mainwindow.o \
		release/moc_mainwindow.o

DIST          =  mainwindow.h main.cpp \
		mainwindow.cpp
QMAKE_TARGET  = serial_port
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = serial_port.exe
DESTDIR_TARGET = release\serial_port.exe

####### Build rules

first: all
all: Makefile.Release  release/serial_port.exe

release/serial_port.exe: D:/Qt/6.9.1/mingw_64/lib/libQt6Charts.a D:/Qt/6.9.1/mingw_64/lib/libQt6OpenGLWidgets.a D:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a D:/Qt/6.9.1/mingw_64/lib/libQt6OpenGL.a D:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a D:/Qt/6.9.1/mingw_64/lib/libQt6SerialPort.a D:/Qt/6.9.1/mingw_64/lib/libQt6Core.a D:/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a ui_mainwindow.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release serial_port.pro

qmake_all: FORCE

dist:
	$(ZIP) serial_port.zip $(SOURCES) $(DIST) serial_port.pro ..\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf ..\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf ..\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf ..\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf ..\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf ..\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf ..\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf ..\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_serialport.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri ..\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf ..\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf ..\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf .qmake.stash ..\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf ..\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf serial_port.pro ..\Qt\6.9.1\mingw_64\lib\Qt6Charts.prl ..\Qt\6.9.1\mingw_64\lib\Qt6OpenGLWidgets.prl ..\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl ..\Qt\6.9.1\mingw_64\lib\Qt6OpenGL.prl ..\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl ..\Qt\6.9.1\mingw_64\lib\Qt6SerialPort.prl ..\Qt\6.9.1\mingw_64\lib\Qt6Core.prl ..\Qt\6.9.1\mingw_64\lib\Qt6EntryPoint.prl    ..\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp mainwindow.h  main.cpp mainwindow.cpp mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\main.o release\mainwindow.o release\moc_mainwindow.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release/moc_predefs.h: ../Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o release\moc_predefs.h ..\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: release/moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_mainwindow.cpp
release/moc_mainwindow.cpp: mainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QThread \
		../Qt/6.9.1/mingw_64/include/QtCore/QVector \
		../Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartView \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QPen \
		../Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QFont \
		../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		../Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		../Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarCategoryAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		../Qt/6.9.1/mingw_64/include/QtCore/QAtomicInt \
		../Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		release/moc_predefs.h \
		../Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include D:/serial_port/release/moc_predefs.h -ID:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -ID:/serial_port -ID:/Qt/6.9.1/mingw_64/include -ID:/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Qt/6.9.1/mingw_64/include/QtGui -ID:/Qt/6.9.1/mingw_64/include/QtSerialPort -ID:/Qt/6.9.1/mingw_64/include/QtCore -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -ID:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -ID:/mingw64/x86_64-w64-mingw32/include mainwindow.h -o release\moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: mainwindow.ui \
		../Qt/6.9.1/mingw_64/bin/uic.exe
	D:\Qt\6.9.1\mingw_64\bin\uic.exe mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release/main.o: main.cpp mainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QThread \
		../Qt/6.9.1/mingw_64/include/QtCore/QVector \
		../Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartView \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QPen \
		../Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QFont \
		../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		../Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		../Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarCategoryAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		../Qt/6.9.1/mingw_64/include/QtCore/QAtomicInt \
		../Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\main.o main.cpp

release/mainwindow.o: mainwindow.cpp mainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		../Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QList \
		../Qt/6.9.1/mingw_64/include/QtCore/QObject \
		../Qt/6.9.1/mingw_64/include/QtCore/QRect \
		../Qt/6.9.1/mingw_64/include/QtCore/QSize \
		../Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		../Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		../Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		../Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QThread \
		../Qt/6.9.1/mingw_64/include/QtCore/QVector \
		../Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartView \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartview.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QPen \
		../Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QFont \
		../Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QChart \
		../Qt/6.9.1/mingw_64/include/QtCharts/qchart.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QLegend \
		../Qt/6.9.1/mingw_64/include/QtCharts/qlegend.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsWidget \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		../Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		../Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGraphicsView \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QAbstractBarSeries \
		../Qt/6.9.1/mingw_64/include/QtCharts/qabstractbarseries.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarSet \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarset.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QBarCategoryAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qbarcategoryaxis.h \
		../Qt/6.9.1/mingw_64/include/QtCharts/QValueAxis \
		../Qt/6.9.1/mingw_64/include/QtCharts/qvalueaxis.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		../Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		../Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		../Qt/6.9.1/mingw_64/include/QtCore/QAtomicInt \
		../Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		../Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPort \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/qserialport.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		../Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/QSerialPortInfo \
		../Qt/6.9.1/mingw_64/include/QtSerialPort/qserialportinfo.h \
		../Qt/6.9.1/mingw_64/include/QtCore/QRandomGenerator \
		../Qt/6.9.1/mingw_64/include/QtCore/qrandom.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\mainwindow.o mainwindow.cpp

release/moc_mainwindow.o: release/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_mainwindow.o release\moc_mainwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

