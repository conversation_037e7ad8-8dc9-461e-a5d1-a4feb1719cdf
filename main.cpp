#include "mainwindow.h"
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 设置应用样式
    a.setStyleSheet(R"(
        QMainWindow {
            background-color: #2D2D30;
        }
        QGroupBox {
            color: #FFFFFF;
            font: bold 10pt;
            border: 1px solid #3F3F46;
            border-radius: 5px;
            margin-top: 1ex;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px;
            background-color: #2D2D30;
            color: #6DB3F2;
        }
        QPushButton {
            background-color: #3F3F46;
            color: #FFFFFF;
            border: 1px solid #6DB3F2;
            border-radius: 4px;
            padding: 5px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #1E1E1E;
        }
        QPushButton:checked {
            background-color: #1C97EA;
        }
        QLabel {
            color: #D0D0D0;
        }
        Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QSlide<PERSON>, QTextEdit {
            background-color: #3F3F46;
            color: #FFFFFF;
            border: 1px solid #6DB3F2;
            border-radius: 4px;
        }
        QSlider::groove:horizontal {
            background: #505050;
            height: 6px;
        }
        QSlider::handle:horizontal {
            background: #6DB3F2;
            border: 1px solid #3C80C3;
            width: 16px;
            margin: -5px 0;
            border-radius: 8px;
        }
    )");

    MainWindow w;
    w.show();

    return a.exec();
}
