#include "mainwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QRandomGenerator>

SerialReader::SerialReader(const QString &portName, int baudRate, QObject *parent)
    : QThread(parent), portName(portName), baudRate(baudRate), running(true) {}

void SerialReader::stop() {
    running = false;
    if (isRunning()) {
        wait(1000);
    }
}

void SerialReader::run() {
    QSerialPort serial;
    serial.setPortName(portName);
    serial.setBaudRate(baudRate);
    serial.setDataBits(QSerialPort::Data8);
    serial.setParity(QSerialPort::NoParity);
    serial.setStopBits(QSerialPort::OneStop);
    serial.setFlowControl(QSerialPort::NoFlowControl);

    if (!serial.open(QIODevice::ReadOnly)) {
        emit dataReady("ERROR:无法打开串口");
        return;
    }

    serial.clear();

    while (running) {
        if (serial.waitForReadyRead(100)) {
            QByteArray data = serial.readAll();
            while (serial.waitForReadyRead(10)) {
                data += serial.readAll();
            }
            QString dataStr = QString::fromUtf8(data).trimmed();
            if (!dataStr.isEmpty()) {
                emit dataReady(dataStr);
            }
        }
    }

    serial.close();
}

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    setWindowTitle("传感器数据可视化软件");
    resize(1000, 700);

    // 初始化传感器配置
    sensorNames = {
        "Normal 1", "Normal 2", "Normal 3", "Normal 4", "Normal 5",
        "Shear 1", "Shear 2", "Shear 3", "Shear 4", "Test"
    };

    scaleFactors = {
        1.0, 1.0, 1.0, 1.0, 1.0,
        1.0, 1.0, 1.0, 1.0, 1.0
    };

    // 创建主控件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    QHBoxLayout *mainLayout = new QHBoxLayout(centralWidget);

    // 左侧控制面板
    QWidget *controlPanel = createControlPanel();
    mainLayout->addWidget(controlPanel, 1);

    // 右侧可视化区域
    QWidget *rightPanel = new QWidget(this);
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);

    // 创建图表视图
    chartView = new QChartView(this);
    chartView->setRenderHint(QPainter::Antialiasing);
    initializeChart();
    rightLayout->addWidget(chartView, 3);

    // 数据日志区域
    logText = new QTextEdit(this);
    logText->setMaximumHeight(150);
    logText->setReadOnly(true);
    rightLayout->addWidget(logText, 1);

    mainLayout->addWidget(rightPanel, 2);

    // 状态栏
    statusBar = new QStatusBar(this);
    setStatusBar(statusBar);
    statusBar->showMessage("请选择串口并连接");

    // 初始化数据
    currentValues = QVector<double>(10, 0.0);
    dataPointsCounter = 0;
    dataRate = 0.0;
    lastTime = QDateTime::currentMSecsSinceEpoch();

    // 启动数据更新计时器
    updateTimer = new QTimer(this);
    updateTimer->setInterval(100); // 10 Hz更新
    connect(updateTimer, &QTimer::timeout, this, &MainWindow::updateDisplay);
    updateTimer->start();

    // 启动模拟数据生成器
    simulationTimer = new QTimer(this);
    simulationTimer->setInterval(200); // 5 Hz模拟数据
    connect(simulationTimer, &QTimer::timeout, this, &MainWindow::generateSimulatedData);
    simulationTimer->start();
}

MainWindow::~MainWindow()
{
    if (serialReader && serialReader->isRunning()) {
        serialReader->stop();
    }
}

void MainWindow::initializeChart() {
    chart = new QChart();
    chart->setTitle("传感器数据");
    chart->setAnimationOptions(QChart::NoAnimation);

    // 创建系列
    barSeries = new QBarSeries();
    chart->addSeries(barSeries);

    // 创建X轴
    axisX = new QBarCategoryAxis();
    for (int i = 0; i < 5; i++) {
        axisX->append(sensorNames[i]);
    }
    chart->addAxis(axisX, Qt::AlignBottom);
    barSeries->attachAxis(axisX);

    // 创建Y轴
    axisY = new QValueAxis();
    axisY->setRange(0, 4095);
    axisY->setTitleText("数值");
    chart->addAxis(axisY, Qt::AlignLeft);
    barSeries->attachAxis(axisY);

    // 创建柱状图
    barSet = new QBarSet("传感器值");
    for (int i = 0; i < 5; i++) {
        *barSet << 0;
    }
    barSeries->append(barSet);

    chartView->setChart(chart);
}

QWidget *MainWindow::createControlPanel() {
    QWidget *panel = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(panel);

    // 串口选择
    QHBoxLayout *portLayout = new QHBoxLayout;
    portLayout->addWidget(new QLabel("串口:"));

    portCombo = new QComboBox(this);
    refreshPorts();
    portLayout->addWidget(portCombo);

    QPushButton *refreshBtn = new QPushButton("刷新", this);
    connect(refreshBtn, &QPushButton::clicked, this, &MainWindow::refreshPorts);
    portLayout->addWidget(refreshBtn);

    layout->addLayout(portLayout);

    // 波特率选择
    QHBoxLayout *baudLayout = new QHBoxLayout;
    baudLayout->addWidget(new QLabel("波特率:"));

    baudCombo = new QComboBox(this);
    baudCombo->addItems({"9600", "19200", "38400", "57600", "115200", "250000"});
    baudCombo->setCurrentText("250000");
    baudLayout->addWidget(baudCombo);
    layout->addLayout(baudLayout);

    // 连接按钮
    connectBtn = new QPushButton("连接串口", this);
    connect(connectBtn, &QPushButton::clicked, this, &MainWindow::toggleConnection);
    layout->addWidget(connectBtn);

    // 数据控制
    QHBoxLayout *controlLayout = new QHBoxLayout;

    QPushButton *clearBtn = new QPushButton("清除日志", this);
    connect(clearBtn, &QPushButton::clicked, this, &MainWindow::clearLog);
    controlLayout->addWidget(clearBtn);

    pauseBtn = new QPushButton("暂停采集", this);
    pauseBtn->setCheckable(true);
    controlLayout->addWidget(pauseBtn);

    layout->addLayout(controlLayout);

    // 缩放因子设置
    QGroupBox *scaleGroup = new QGroupBox("传感器缩放因子", this);
    QVBoxLayout *scaleLayout = new QVBoxLayout(scaleGroup);

    for (int i = 0; i < 5; i++) {
        QHBoxLayout *sliderLayout = new QHBoxLayout;
        sliderLayout->addWidget(new QLabel(sensorNames[i] + ":"));

        QSlider *slider = new QSlider(Qt::Horizontal, this);
        slider->setRange(1, 100);
        slider->setValue(static_cast<int>(scaleFactors[i] * 10));
        connect(slider, &QSlider::valueChanged, [this, i](int value) {
            updateScaleFactor(i, value / 10.0);
        });
        sliderLayout->addWidget(slider);

        scaleLayout->addLayout(sliderLayout);
    }

    layout->addWidget(scaleGroup);

    // 信息面板
    QGroupBox *infoGroup = new QGroupBox("数据信息", this);
    QVBoxLayout *infoLayout = new QVBoxLayout(infoGroup);

    dataCountLabel = new QLabel("数据包数量: 0", this);
    infoLayout->addWidget(dataCountLabel);

    dataRateLabel = new QLabel("数据速率: 0 Hz", this);
    infoLayout->addWidget(dataRateLabel);

    infoGroup->setLayout(infoLayout);
    layout->addWidget(infoGroup);

    // 添加数据说明
    QLabel *helpLabel = new QLabel("数据格式示例: 逗号分隔的10个值", this);
    helpLabel->setStyleSheet("font-style: italic; color: #888;");
    layout->addWidget(helpLabel);

    layout->addStretch();

    return panel;
}

void MainWindow::refreshPorts() {
    portCombo->clear();
    QList<QSerialPortInfo> ports = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &port : ports) {
        portCombo->addItem(port.portName());
    }
    if (ports.isEmpty()) {
        statusBar->showMessage("未找到可用串口");
    } else {
        statusBar->showMessage(QString("找到 %1 个串口").arg(ports.size()));
    }
}

void MainWindow::toggleConnection() {
    if (serialReader && serialReader->isRunning()) {
        serialReader->stop();
        serialReader = nullptr;
        connectBtn->setText("连接串口");
        statusBar->showMessage("串口已断开");
    } else {
        QString port = portCombo->currentText();
        if (port.isEmpty()) {
            statusBar->showMessage("请选择有效的串口");
            return;
        }

        bool ok;
        int baud = baudCombo->currentText().toInt(&ok);
        if (!ok) {
            statusBar->showMessage("无效的波特率");
            return;
        }

        serialReader = new SerialReader(port, baud, this);
        connect(serialReader, &SerialReader::dataReady, this, &MainWindow::processData);
        serialReader->start();
        connectBtn->setText("断开串口");
        statusBar->showMessage(QString("已连接 %1 (%2 baud)").arg(port).arg(baud));
        dataPointsCounter = 0;
        lastTime = QDateTime::currentMSecsSinceEpoch();
    }
}

void MainWindow::processData(const QString &data) {
    if (pauseBtn->isChecked()) {
        return;
    }

    if (data.startsWith("ERROR:")) {
        logText->append("错误: " + data.mid(6));
        return;
    }

    try {
        // 记录原始数据
        logText->append("接收: " + data);

        // 解析数据
        QStringList dataList = data.split(',');
        QVector<double> values;
        for (int i = 0; i < 10; i++) {
            if (i < dataList.size()) {
                values.append(dataList[i].toDouble());
            } else {
                values.append(0.0);
            }
        }

        // 应用缩放因子
        for (int i = 0; i < values.size(); i++) {
            values[i] *= scaleFactors[i];
        }
        currentValues = values;

        // 更新数据计数
        dataPointsCounter++;

        // 更新数据速率计算
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        double elapsed = (currentTime - lastTime) / 1000.0;
        if (elapsed > 0.5) { // 每0.5秒更新一次速率显示
            dataRate = dataPointsCounter / elapsed;
            dataPointsCounter = 0;
            lastTime = currentTime;
        }
    } catch (...) {
        logText->append("数据解析错误");
    }
}

void MainWindow::updateDisplay() {
    // 更新信息面板
    dataCountLabel->setText(QString("数据包数量: %1").arg(dataPointsCounter));
    dataRateLabel->setText(QString("数据速率: %1 Hz").arg(dataRate, 0, 'f', 1));

    // 更新图表
    updateChart();
}

void MainWindow::updateScaleFactor(int index, double value) {
    if (index >= 0 && index < scaleFactors.size()) {
        scaleFactors[index] = value;
        logText->append(QString("更新缩放因子 %1: %2").arg(sensorNames[index]).arg(value));
    }
}

void MainWindow::clearLog() {
    logText->clear();
}

void MainWindow::updateChart() {
    if (currentValues.size() < 5) return;

    // 更新柱状图数据
    barSet->remove(0, barSet->count());
    for (int i = 0; i < 5; i++) {
        *barSet << currentValues[i];
    }

    // 更新图表标题
    chart->setTitle(QString("传感器数据 - %1").arg(QDateTime::currentDateTime().toString("HH:mm:ss")));
}

void MainWindow::generateSimulatedData() {
    // 生成模拟传感器数据
    QVector<double> values;
    for (int i = 0; i < 10; i++) {
        double baseValue = QRandomGenerator::global()->bounded(500, 3500);
        double noise = QRandomGenerator::global()->bounded(-100, 100);
        values.append(baseValue + noise);
    }

    // 应用缩放因子
    for (int i = 0; i < values.size(); i++) {
        values[i] *= scaleFactors[i];
    }
    currentValues = values;

    // 更新数据计数
    dataPointsCounter++;

    // 更新数据速率计算
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    double elapsed = (currentTime - lastTime) / 1000.0;
    if (elapsed > 0.5) { // 每0.5秒更新一次速率显示
        dataRate = dataPointsCounter / elapsed;
        dataPointsCounter = 0;
        lastTime = currentTime;
    }

    // 记录模拟数据
    QString dataStr;
    for (int i = 0; i < values.size(); i++) {
        dataStr += QString::number(values[i], 'f', 1);
        if (i < values.size() - 1) dataStr += ",";
    }
    logText->append("模拟数据: " + dataStr);
}
