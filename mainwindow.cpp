#include "mainwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QRandomGenerator>

// 使用常量命名空间
using namespace SerialPortConstants;

SerialReader::SerialReader(const QString &portName, int baudRate, QObject *parent)
    : QThread(parent), portName(portName), baudRate(baudRate), running(1), connected(false) {}

void SerialReader::stop() {
    running.storeRelease(0); // 原子操作设置停止标志
    if (isRunning()) {
        // 增加超时时间，确保线程能够正常退出
        if (!wait(THREAD_WAIT_TIMEOUT_MS)) {
            qWarning() << "SerialReader线程强制终止";
            terminate(); // 强制终止线程（最后手段）
            wait(1000);
        }
    }
}

bool SerialReader::isConnected() const {
    QMutexLocker locker(&connectionMutex);
    return connected;
}

void SerialReader::run() {
    QSerialPort serial;
    serial.setPortName(portName);
    serial.setBaudRate(baudRate);
    serial.setDataBits(QSerialPort::Data8);
    serial.setParity(QSerialPort::NoParity);
    serial.setStopBits(QSerialPort::OneStop);
    serial.setFlowControl(QSerialPort::NoFlowControl);

    if (!serial.open(QIODevice::ReadOnly)) {
        QString errorMsg = QString("无法打开串口 %1: %2").arg(portName).arg(serial.errorString());
        emit connectionError(errorMsg);
        {
            QMutexLocker locker(&connectionMutex);
            connected = false;
        }
        return;
    }

    {
        QMutexLocker locker(&connectionMutex);
        connected = true;
    }

    serial.clear();
    qDebug() << "串口连接成功:" << portName << "波特率:" << baudRate;

    while (running.loadAcquire()) { // 使用原子操作检查运行状态
        if (serial.waitForReadyRead(100)) {
            QByteArray data = serial.readAll();
            // 确保读取完整数据包
            while (serial.waitForReadyRead(10)) {
                data += serial.readAll();
            }
            QString dataStr = QString::fromUtf8(data).trimmed();
            if (!dataStr.isEmpty()) {
                emit dataReady(dataStr);
            }
        }

        // 检查串口错误状态
        if (serial.error() != QSerialPort::NoError && serial.error() != QSerialPort::TimeoutError) {
            QString errorMsg = QString("串口通信错误: %1").arg(serial.errorString());
            emit connectionError(errorMsg);
            break;
        }
    }

    {
        QMutexLocker locker(&connectionMutex);
        connected = false;
    }

    serial.close();
    qDebug() << "串口连接已关闭:" << portName;
}

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    setWindowTitle("传感器数据可视化软件");
    resize(1000, 700);

    // 初始化传感器配置
    sensorNames = {
        "Normal 1", "Normal 2", "Normal 3", "Normal 4", "Normal 5",
        "Shear 1", "Shear 2", "Shear 3", "Shear 4", "Test"
    };

    scaleFactors = QVector<double>(SENSOR_COUNT, 1.0);

    // 创建主控件
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    QHBoxLayout *mainLayout = new QHBoxLayout(centralWidget);

    // 左侧控制面板
    QWidget *controlPanel = createControlPanel();
    mainLayout->addWidget(controlPanel, 1);

    // 右侧可视化区域
    QWidget *rightPanel = new QWidget(this);
    QVBoxLayout *rightLayout = new QVBoxLayout(rightPanel);

    // 创建图表视图
    chartView = new QChartView(this);
    chartView->setRenderHint(QPainter::Antialiasing);
    initializeChart();
    rightLayout->addWidget(chartView, 3);

    // 数据日志区域
    logText = new QTextEdit(this);
    logText->setMaximumHeight(150);
    logText->setReadOnly(true);
    rightLayout->addWidget(logText, 1);

    mainLayout->addWidget(rightPanel, 2);

    // 状态栏
    statusBar = new QStatusBar(this);
    setStatusBar(statusBar);
    statusBar->showMessage("请选择串口并连接");

    // 初始化数据
    currentValues = QVector<double>(SENSOR_COUNT, 0.0);
    dataPointsCounter = 0;
    dataRate = 0.0;
    lastTime = QDateTime::currentMSecsSinceEpoch();

    // 启动数据更新计时器
    updateTimer = new QTimer(this);
    updateTimer->setInterval(UPDATE_INTERVAL_MS);
    connect(updateTimer, &QTimer::timeout, this, &MainWindow::updateDisplay);
    updateTimer->start();

    // 启动模拟数据生成器
    simulationTimer = new QTimer(this);
    simulationTimer->setInterval(SIMULATION_INTERVAL_MS);
    connect(simulationTimer, &QTimer::timeout, this, &MainWindow::generateSimulatedData);
    simulationTimer->start();
}

MainWindow::~MainWindow()
{
    // 停止定时器
    if (updateTimer) {
        updateTimer->stop();
    }
    if (simulationTimer) {
        simulationTimer->stop();
    }

    // 停止串口线程
    if (serialReader && serialReader->isRunning()) {
        qDebug() << "正在停止串口线程...";
        serialReader->stop();
        qDebug() << "串口线程已停止";
    }

    qDebug() << "MainWindow析构完成";
}

void MainWindow::initializeChart() {
    chart = new QChart();
    chart->setTitle("传感器数据");
    chart->setAnimationOptions(QChart::NoAnimation);

    // 创建系列
    barSeries = new QBarSeries();
    chart->addSeries(barSeries);

    // 创建X轴
    axisX = new QBarCategoryAxis();
    for (int i = 0; i < DISPLAY_SENSOR_COUNT; i++) {
        axisX->append(sensorNames[i]);
    }
    chart->addAxis(axisX, Qt::AlignBottom);
    barSeries->attachAxis(axisX);

    // 创建Y轴
    axisY = new QValueAxis();
    axisY->setRange(0, MAX_CHART_VALUE);
    axisY->setTitleText("数值");
    chart->addAxis(axisY, Qt::AlignLeft);
    barSeries->attachAxis(axisY);

    // 创建柱状图
    barSet = new QBarSet("传感器值");
    for (int i = 0; i < DISPLAY_SENSOR_COUNT; i++) {
        *barSet << 0;
    }
    barSeries->append(barSet);

    chartView->setChart(chart);
}

QWidget *MainWindow::createControlPanel() {
    QWidget *panel = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(panel);

    // 串口选择
    QHBoxLayout *portLayout = new QHBoxLayout;
    portLayout->addWidget(new QLabel("串口:"));

    portCombo = new QComboBox(this);
    refreshPorts();
    portLayout->addWidget(portCombo);

    QPushButton *refreshBtn = new QPushButton("刷新", this);
    connect(refreshBtn, &QPushButton::clicked, this, &MainWindow::refreshPorts);
    portLayout->addWidget(refreshBtn);

    layout->addLayout(portLayout);

    // 波特率选择
    QHBoxLayout *baudLayout = new QHBoxLayout;
    baudLayout->addWidget(new QLabel("波特率:"));

    baudCombo = new QComboBox(this);
    baudCombo->addItems({"9600", "19200", "38400", "57600", "115200", "250000"});
    baudCombo->setCurrentText(QString::number(DEFAULT_BAUD_RATE));
    baudLayout->addWidget(baudCombo);
    layout->addLayout(baudLayout);

    // 连接按钮
    connectBtn = new QPushButton("连接串口", this);
    connect(connectBtn, &QPushButton::clicked, this, &MainWindow::toggleConnection);
    layout->addWidget(connectBtn);

    // 数据控制
    QHBoxLayout *controlLayout = new QHBoxLayout;

    QPushButton *clearBtn = new QPushButton("清除日志", this);
    connect(clearBtn, &QPushButton::clicked, this, &MainWindow::clearLog);
    controlLayout->addWidget(clearBtn);

    pauseBtn = new QPushButton("暂停采集", this);
    pauseBtn->setCheckable(true);
    controlLayout->addWidget(pauseBtn);

    layout->addLayout(controlLayout);

    // 缩放因子设置
    QGroupBox *scaleGroup = new QGroupBox("传感器缩放因子", this);
    QVBoxLayout *scaleLayout = new QVBoxLayout(scaleGroup);

    for (int i = 0; i < DISPLAY_SENSOR_COUNT; i++) {
        QHBoxLayout *sliderLayout = new QHBoxLayout;
        sliderLayout->addWidget(new QLabel(sensorNames[i] + ":"));

        QSlider *slider = new QSlider(Qt::Horizontal, this);
        slider->setRange(1, 100);
        slider->setValue(static_cast<int>(scaleFactors[i] * 10));
        connect(slider, &QSlider::valueChanged, [this, i](int value) {
            updateScaleFactor(i, value / 10.0);
        });
        sliderLayout->addWidget(slider);

        scaleLayout->addLayout(sliderLayout);
    }

    layout->addWidget(scaleGroup);

    // 信息面板
    QGroupBox *infoGroup = new QGroupBox("数据信息", this);
    QVBoxLayout *infoLayout = new QVBoxLayout(infoGroup);

    dataCountLabel = new QLabel("数据包数量: 0", this);
    infoLayout->addWidget(dataCountLabel);

    dataRateLabel = new QLabel("数据速率: 0 Hz", this);
    infoLayout->addWidget(dataRateLabel);

    infoGroup->setLayout(infoLayout);
    layout->addWidget(infoGroup);

    // 添加数据说明
    QLabel *helpLabel = new QLabel("数据格式示例: 逗号分隔的10个值", this);
    helpLabel->setStyleSheet("font-style: italic; color: #888;");
    layout->addWidget(helpLabel);

    layout->addStretch();

    return panel;
}

void MainWindow::refreshPorts() {
    portCombo->clear();
    QList<QSerialPortInfo> ports = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &port : ports) {
        portCombo->addItem(port.portName());
    }
    if (ports.isEmpty()) {
        statusBar->showMessage("未找到可用串口");
    } else {
        statusBar->showMessage(QString("找到 %1 个串口").arg(ports.size()));
    }
}

void MainWindow::toggleConnection() {
    if (serialReader && serialReader->isRunning()) {
        qDebug() << "断开串口连接...";
        serialReader->stop();
        serialReader.reset(); // 使用智能指针的reset方法安全释放
        connectBtn->setText("连接串口");
        statusBar->showMessage("串口已断开");
        qDebug() << "串口连接已断开";
    } else {
        QString port = portCombo->currentText();
        if (port.isEmpty()) {
            statusBar->showMessage("请选择有效的串口");
            logText->append("错误: 未选择串口");
            return;
        }

        bool ok;
        int baud = baudCombo->currentText().toInt(&ok);
        if (!ok || baud <= 0) {
            statusBar->showMessage("无效的波特率");
            logText->append("错误: 波特率无效");
            return;
        }

        qDebug() << "尝试连接串口:" << port << "波特率:" << baud;

        // 使用智能指针创建SerialReader对象
        serialReader.reset(new SerialReader(port, baud, this));

        // 连接信号
        connect(serialReader.data(), &SerialReader::dataReady, this, &MainWindow::processData);
        connect(serialReader.data(), &SerialReader::connectionError, this, [this](const QString &error) {
            logText->append("连接错误: " + error);
            statusBar->showMessage("连接失败: " + error);
            connectBtn->setText("连接串口");
        });

        serialReader->start();
        connectBtn->setText("断开串口");
        statusBar->showMessage(QString("正在连接 %1 (%2 baud)...").arg(port).arg(baud));

        // 重置数据计数器
        {
            QMutexLocker locker(&dataMutex);
            dataPointsCounter = 0;
            lastTime = QDateTime::currentMSecsSinceEpoch();
        }
    }
}

void MainWindow::processData(const QString &data) {
    if (pauseBtn->isChecked()) {
        return;
    }

    if (data.startsWith("ERROR:")) {
        logText->append("错误: " + data.mid(6));
        return;
    }

    try {
        // 记录原始数据
        logText->append("接收: " + data);

        // 解析数据
        QStringList dataList = data.split(',');
        if (dataList.isEmpty()) {
            logText->append("警告: 接收到空数据");
            return;
        }

        QVector<double> values;
        bool hasValidData = false;

        for (int i = 0; i < SENSOR_COUNT; i++) {
            if (i < dataList.size()) {
                bool ok;
                double value = dataList[i].trimmed().toDouble(&ok);
                if (ok) {
                    values.append(value);
                    hasValidData = true;
                } else {
                    values.append(0.0);
                    qDebug() << "数据转换失败，索引:" << i << "值:" << dataList[i];
                }
            } else {
                values.append(0.0);
            }
        }

        if (!hasValidData) {
            logText->append("警告: 没有有效的数值数据");
            return;
        }

        // 使用互斥锁保护共享数据
        {
            QMutexLocker locker(&dataMutex);

            // 应用缩放因子
            for (int i = 0; i < values.size() && i < scaleFactors.size(); i++) {
                values[i] *= scaleFactors[i];
            }
            currentValues = values;

            // 更新数据计数
            dataPointsCounter++;

            // 更新数据速率计算
            qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
            double elapsed = (currentTime - lastTime) / 1000.0;
            if (elapsed > RATE_UPDATE_INTERVAL_SEC) {
                dataRate = dataPointsCounter / elapsed;
                dataPointsCounter = 0;
                lastTime = currentTime;
            }
        }

    } catch (const std::exception &e) {
        QString errorMsg = QString("数据解析异常: %1").arg(e.what());
        logText->append(errorMsg);
        qWarning() << errorMsg;
    } catch (...) {
        QString errorMsg = "未知数据解析错误";
        logText->append(errorMsg);
        qWarning() << errorMsg;
    }
}

void MainWindow::updateDisplay() {
    // 使用互斥锁保护数据读取
    int currentDataCount;
    double currentDataRate;
    {
        QMutexLocker locker(&dataMutex);
        currentDataCount = dataPointsCounter;
        currentDataRate = dataRate;
    }

    // 更新信息面板
    dataCountLabel->setText(QString("数据包数量: %1").arg(currentDataCount));
    dataRateLabel->setText(QString("数据速率: %1 Hz").arg(currentDataRate, 0, 'f', 1));

    // 更新图表
    updateChart();
}

void MainWindow::updateScaleFactor(int index, double value) {
    if (index >= 0 && index < scaleFactors.size()) {
        scaleFactors[index] = value;
        logText->append(QString("更新缩放因子 %1: %2").arg(sensorNames[index]).arg(value));
    }
}

void MainWindow::clearLog() {
    logText->clear();
}

void MainWindow::updateChart() {
    QVector<double> values;
    {
        QMutexLocker locker(&dataMutex);
        values = currentValues; // 复制数据避免长时间持有锁
    }

    if (values.size() < DISPLAY_SENSOR_COUNT) {
        return;
    }

    // 更新柱状图数据
    barSet->remove(0, barSet->count());
    for (int i = 0; i < DISPLAY_SENSOR_COUNT; i++) {
        *barSet << values[i];
    }

    // 更新图表标题
    chart->setTitle(QString("传感器数据 - %1").arg(QDateTime::currentDateTime().toString("HH:mm:ss")));
}

void MainWindow::generateSimulatedData() {
    // 只在没有真实串口连接时生成模拟数据
    if (serialReader && serialReader->isConnected()) {
        return;
    }

    // 生成模拟传感器数据
    QVector<double> values;
    for (int i = 0; i < SENSOR_COUNT; i++) {
        double baseValue = QRandomGenerator::global()->bounded(500, 3500);
        double noise = QRandomGenerator::global()->bounded(-100, 100);
        values.append(baseValue + noise);
    }

    // 使用互斥锁保护共享数据
    {
        QMutexLocker locker(&dataMutex);

        // 应用缩放因子
        for (int i = 0; i < values.size() && i < scaleFactors.size(); i++) {
            values[i] *= scaleFactors[i];
        }
        currentValues = values;

        // 更新数据计数
        dataPointsCounter++;

        // 更新数据速率计算
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        double elapsed = (currentTime - lastTime) / 1000.0;
        if (elapsed > RATE_UPDATE_INTERVAL_SEC) {
            dataRate = dataPointsCounter / elapsed;
            dataPointsCounter = 0;
            lastTime = currentTime;
        }
    }

    // 记录模拟数据
    QString dataStr;
    for (int i = 0; i < values.size(); i++) {
        dataStr += QString::number(values[i], 'f', 1);
        if (i < values.size() - 1) dataStr += ",";
    }
    logText->append("模拟数据: " + dataStr);
}
