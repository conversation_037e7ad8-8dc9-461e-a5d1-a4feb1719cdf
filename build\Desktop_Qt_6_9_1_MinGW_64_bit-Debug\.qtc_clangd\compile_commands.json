[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port", "-ID:\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\main.cpp"], "directory": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port", "-ID:\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\mainwindow.cpp"], "directory": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port", "-ID:\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\mainwindow.h"], "directory": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-DQT_QML_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_SERIALPORT_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-ID:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port", "-ID:\\Qt\\6.9.1\\mingw_64\\include", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCharts", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGLWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtSerialPort", "-ID:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\debug", "-ID:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug", "-ID:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "D:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "D:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\HuaweiMoveData\\Users\\京川\\Documents\\serial_port\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\ui_mainwindow.h"], "directory": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/HuaweiMoveData/Users/<USER>/Documents/serial_port/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/ui_mainwindow.h"}]